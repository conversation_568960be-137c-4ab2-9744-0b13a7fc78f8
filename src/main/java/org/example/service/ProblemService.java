package org.example.service;

import org.example.model.Difficulty;
import org.example.model.Problem;
import org.example.repository.ProblemRepository;

import java.util.Collection;
import java.util.NoSuchElementException;
import java.util.Set;

import static org.example.util.Validation.validate;

public class ProblemService {

    private final ProblemRepository repo;

    public ProblemService(ProblemRepository repo) {
            this.repo = repo;
    }

    public Problem addProblem(String name, String description, Set<String> tags, Difficulty difficulty, int baseScore, String strategy) {

        validate(name != null && !name.isBlank(), "Problem name required");
        validate(description != null && !description.isBlank(), "Problem description required");
        validate(difficulty != null, "Difficulty required");
        validate(baseScore >= 0, "Base score must be >= 0");

        return repo.save(name.trim(), description.trim(), tags != null? tags : Set.of(), difficulty, baseScore, strategy);
    }

    Problem get(long id) {
        return repo.findById(id).orElseThrow(() ->
                new NoSuchElementException("Problem not found: " + id));
    }

    Collection<Problem> all() { return repo.findAll(); }
}