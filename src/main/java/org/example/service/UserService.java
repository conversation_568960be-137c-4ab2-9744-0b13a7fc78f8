package org.example.service;

import org.example.model.User;
import org.example.repository.UserRepository;

import java.util.NoSuchElementException;

import static org.example.util.Validation.validate;

public class UserService {

    private final UserRepository repo;

    public UserService(UserRepository repo) {
        this.repo = repo;
    }

    public User addUser(String name, String department) {

        validate(name != null && !name.isBlank(), "User name required");
        validate(department != null && !department.isBlank(), "Department required");

        return repo.save(name.trim(), department.trim());
    }
    User get(long id) {
        return repo.findById(id).orElseThrow(() ->
                new NoSuchElementException("User not found: " + id));
    }
}
