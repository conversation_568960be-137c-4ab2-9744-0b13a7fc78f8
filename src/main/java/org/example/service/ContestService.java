package org.example.service;

import org.example.dto.ProblemQuery;
import org.example.dto.ProblemView;
import org.example.dto.SolveResult;
import org.example.dto.SortSpec;
import org.example.model.Problem;
import org.example.model.Submission;
import org.example.model.User;
import org.example.repository.SubmissionRepository;
import org.example.scoring.StrategyRegistry;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.example.util.Validation.validate;

public class ContestService {

    private final ProblemService problemService;
    private final UserService userService;
    private final SubmissionRepository submissionRepo;
    private final StrategyRegistry strategies;
    private final long contestStartMs;
    private final long contestEndMs;

    public ContestService(ProblemService problemService, UserService userService,
                          SubmissionRepository submissionRepo, StrategyRegistry strategies,
                          long contestStartMs, long contestEndMs) {

        this.problemService = problemService;
        this.userService = userService;
        this.submissionRepo = submissionRepo;
        this.strategies = strategies;
        this.contestStartMs = contestStartMs;
        this.contestEndMs = contestEndMs;

    }

    /**
     * user solving the problem
     *
      * @param userId
     * @param problemId
     * @param timeTakenSec
     * @return
     */
    public SolveResult solve(long userId, long problemId, long timeTakenSec) {

        User u = userService.get(userId);
        Problem p = problemService.get(problemId);

        validate(timeTakenSec >= 0, "timeTakenSec must be >= 0");

        long now = System.currentTimeMillis();

        validate(now >= contestStartMs && now <= contestEndMs, "Submission outside contest window");

        if (submissionRepo.findByUserAndProblem(userId, problemId).isPresent())
            throw new IllegalStateException("Problem already solved by user");

        int score = strategies.get(p.scoringStrategy).computeScore(p, timeTakenSec);

        Submission s = new Submission(u.id, p.id, timeTakenSec, now, score);
        submissionRepo.save(s);

        int newTotal = totalScoreForUser(userId);

        return new SolveResult(s, newTotal);
    }

    /**
     * fetch all problems solved by user
     *
     * @param userId
     * @return
     */
    public List<Submission> fetchSolvedProblems(long userId) {

        userService.get(userId); // validate existence

        return submissionRepo.findByUser(userId).stream()
                .sorted(Comparator.comparingLong(sub -> sub.solvedAtMs))
                .collect(Collectors.toList());
    }

    /**
     * fetchProblems(query) with filters + sorting and extension stats
     *
     * @param q
     * @return
     */
    public List<ProblemView> fetchProblems(ProblemQuery q) {

        Collection<Problem> all = problemService.all();

        List<Problem> filtered = all.stream().filter(p -> {
            boolean ok = true;
            if (q.difficulty != null) ok &= p.difficulty == q.difficulty;
            if (q.tags != null && !q.tags.isEmpty()) ok &= p.tags.containsAll(q.tags);
            return ok;
        }).collect(Collectors.toList());

        // compute extension stats once
        Map<Long, List<Submission>> subsByProblem = filtered.stream()
                .collect(Collectors.toMap(p -> p.id, p -> submissionRepo.findByProblem(p.id)));

        List<ProblemView> views = filtered.stream().map(p -> {
            List<Submission> subs = subsByProblem.getOrDefault(p.id, List.of());
            int cnt = subs.size();
            OptionalDouble avg = subs.stream().mapToLong(s -> s.timeTakenSec).average();
            return new ProblemView(p, cnt, avg);
        }).collect(Collectors.toList());

            Comparator<ProblemView> cmp = buildComparator(q.sortBy);
            views.sort(cmp);

        return views;
    }


    /**
     * get leader for the contest
     *
     * @return
     */
    public Map.Entry<User, Integer> getLeader() {

        Map<Long, Integer> totals = submissionRepo.findAll().stream()
                .filter(s -> s.solvedAtMs >= contestStartMs && s.solvedAtMs <= contestEndMs)
                .collect(Collectors.groupingBy(s -> s.userId, Collectors.summingInt(s -> s.awardedScore)));

        if (totals.isEmpty())
            throw new NoSuchElementException("No submissions yet");

        long winnerId = totals.entrySet().stream().max(Map.Entry.comparingByValue()).get().getKey();

        User u = userService.get(winnerId);

        return Map.entry(u, totals.get(winnerId));
    }

    int totalScoreForUser(long userId) {

        return submissionRepo.findByUser(userId).stream()
                .filter(s -> s.solvedAtMs >= contestStartMs && s.solvedAtMs <= contestEndMs)
                .mapToInt(s -> s.awardedScore).sum();
    }

    /**
     *  comparator for sortSpec
     *
     * @param specs
     * @return
     */
    private Comparator<ProblemView> buildComparator(List<SortSpec> specs) {
        if (specs == null || specs.isEmpty()) specs = List.of(SortSpec.desc("score"), SortSpec.asc("name"));

        Map<String, Function<ProblemView, Comparable<?>>> keyMap = new HashMap<>();
        keyMap.put("name", pv -> pv.name.toLowerCase(Locale.ROOT));
        keyMap.put("score", pv -> pv.baseScore);
        keyMap.put("difficulty", pv -> pv.difficulty.ordinal());
        keyMap.put("solvedCount", pv -> pv.solvedCount);
        keyMap.put("avgTime", pv -> pv.avgTimeSec.orElse(Double.POSITIVE_INFINITY));

        Comparator<ProblemView> cmp = Comparator.comparing(pv -> 0); // neutral

        for (SortSpec s : specs) {
            Function<ProblemView, Comparable<?>> keyFn = keyMap.get(s.field);
            if (keyFn == null)
                throw new IllegalArgumentException("Unsupported sort field: " + s.field);

            Comparator next =
                    Comparator.comparing(
                            (ProblemView pv) -> (Comparable) keyFn.apply(pv),
                            Comparator.nullsLast(Comparator.naturalOrder())
                    );

            if (!s.asc) next = next.reversed();

            cmp = cmp.thenComparing(next);

        }
        return cmp;
    }

}
