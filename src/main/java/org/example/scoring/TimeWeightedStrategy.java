package org.example.scoring;

import org.example.model.Problem;

/** Score = baseScore + max(0, timeBonus - k * time). Keeps it simple and testable. */
public class TimeWeightedStrategy implements ScoringStrategy {

    private  int timeBonus; // e.g., 100
    private  int k;         // penalty per second, e.g., 1

    public TimeWeightedStrategy(int timeBonus, int k) {

            this.timeBonus = timeBonus;
            this.k = k;
    }

    @Override
    public String name() {
        return "time_weighted";
    }

    @Override
    public int computeScore(Problem p, long timeTakenSec) {

            long extra = Math.max(0L, (long) timeBonus - k * timeTakenSec);
            long total = (long) p.baseScore + extra;

        return (int) Math.max(0, Math.min(Integer.MAX_VALUE, total));
    }
}