package org.example.scoring;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

public class StrategyRegistry {

    private  Map<String, ScoringStrategy> registry = new ConcurrentHashMap<>();

    public StrategyRegistry register(ScoringStrategy s) {
        registry.put(s.name(), s);
        return this;
    }

    public ScoringStrategy get(String name) {
        return Optional.ofNullable(registry.get(name))
            .orElseThrow(() -> new IllegalArgumentException("Unknown scoring strategy: " + name));
    }
}