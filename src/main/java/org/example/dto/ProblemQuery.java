package org.example.dto;

import org.example.model.Difficulty;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class ProblemQuery {

    public Difficulty difficulty;                // optional
    public Set<String> tags;                     // optional, all must match if provided
    public List<SortSpec> sortBy = new ArrayList<>(); // defaults to score desc, name asc

    public static ProblemQuery of() {
        return new ProblemQuery();
    }

    public ProblemQuery difficulty(Difficulty d) {
        this.difficulty = d;
        return this;
    }

    public ProblemQuery tags(Set<String> t) {
        this.tags = t == null? null : t.stream().map(String::toLowerCase).collect(Collectors.toSet());
        return this;
    }

    public ProblemQuery sortBy(SortSpec... specs) {
        this.sortBy = Arrays.asList(specs);
        return this;
    }
}