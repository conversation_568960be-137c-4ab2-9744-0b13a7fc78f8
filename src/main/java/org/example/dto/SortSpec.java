package org.example.dto;

public class SortSpec {

    public final String field; // name, score, difficulty, solvedCount, avgTime // To do : Can create enum here
    public final boolean asc;

    public SortSpec(String field, boolean asc) {
        this.field = field;
        this.asc = asc;
    }

    public static SortSpec asc(String f) {
        return new SortSpec(f, true);
    }

    public static SortSpec desc(String f) {
        return new SortSpec(f, false);
    }
}
