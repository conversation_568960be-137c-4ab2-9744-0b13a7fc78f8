package org.example.dto;

import org.example.model.Submission;

import java.util.Locale;

public class SolveResult {

    private final Submission submission;
    private final int newTotalScore;

    public SolveResult(Submission submission, int newTotalScore) {
        this.submission = submission;
        this.newTotalScore = newTotalScore;
    }

    @Override
    public String toString() {
        return String.format(Locale.ROOT,
                "Solved problem #%d for %d pts (time=%ds). New total=%d",
                submission.problemId, submission.awardedScore, submission.timeTakenSec, newTotalScore);
    }
}
