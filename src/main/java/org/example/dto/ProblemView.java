package org.example.dto;

import org.example.model.Difficulty;
import org.example.model.Problem;

import java.util.Locale;
import java.util.OptionalDouble;
import java.util.Set;

/* ======================= DTOs & Query Objects ======================= */
public class ProblemView {

    final long id;
    public final String name;
    final Set<String> tags;
    public final Difficulty difficulty;
    public final int baseScore;
    final String scoringStrategy;

    // extension fields
    public final int solvedCount;
    public final OptionalDouble avgTimeSec;

    public ProblemView(Problem p, int solvedCount, OptionalDouble avgTimeSec) {

        this.id = p.id;
        this.name = p.name;
        this.tags = p.tags;
        this.difficulty = p.difficulty;
        this.baseScore = p.baseScore;
        this.scoringStrategy = p.scoringStrategy;
        this.solvedCount = solvedCount;
        this.avgTimeSec = avgTimeSec;
    }

    @Override public String toString() {
        return String.format(Locale.ROOT,
                "[#%d] %s | tags=%s | %s | baseScore=%d | strat=%s | solved=%d | avgTime=%s",
                id, name, tags, difficulty, baseScore, scoringStrategy, solvedCount,
                avgTimeSec.isPresent()? String.format(Locale.ROOT, "%.2fs", avgTimeSec.getAsDouble()) : "n/a");
    }

}
