package org.example.model;

import java.util.*;
import java.util.stream.Collectors;

public  class Problem {

    public long id;
    public String name;
    public  String description;
    public  Set<String> tags;
    public  Difficulty difficulty;
    public  int baseScore;
    public  String scoringStrategy;

    public Problem(long id, String name, String description, Set<String> tags,
                   Difficulty difficulty, int baseScore, String scoringStrategy) {
        this.id = id;
        this.name = Objects.requireNonNull(name);
        this.description = Objects.requireNonNull(description);
        this.tags = Collections.unmodifiableSet(
                tags.stream().map(String::toLowerCase).collect(Collectors.toSet())
        );
        this.difficulty = Objects.requireNonNull(difficulty);
        this.baseScore = baseScore;
        this.scoringStrategy = Objects.requireNonNull(scoringStrategy);
    }
}
