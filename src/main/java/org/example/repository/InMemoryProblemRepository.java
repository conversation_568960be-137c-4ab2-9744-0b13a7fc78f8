package org.example.repository;

import org.example.model.Difficulty;
import org.example.model.Problem;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

public class InMemoryProblemRepository implements ProblemRepository {

    private  Map<Long, Problem> store = new ConcurrentHashMap<>();
    private  AtomicLong seq = new AtomicLong(1);

    @Override
    public Problem save(String name, String description, Set<String> tags,
                        Difficulty difficulty, int baseScore, String scoringStrategy) {
        long id = seq.getAndIncrement();
        Problem p = new Problem(id, name, description, tags, difficulty, baseScore, scoringStrategy);
        store.put(id, p);
        return p;
    }

    @Override
    public Optional<Problem> findById(long id) { return Optional.ofNullable(store.get(id)); }

    @Override
    public Collection<Problem> findAll() { return store.values(); }
}
