package org.example.repository;

import org.example.model.Submission;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

public class InMemorySubmissionRepository implements SubmissionRepository {

    private Map<Long, List<Submission>> byUser = new ConcurrentHashMap<>();
    private Map<Long, List<Submission>> byProblem = new ConcurrentHashMap<>();
    private Set<String> uniqueKey = Collections.newSetFromMap(new ConcurrentHashMap<>()); // userId#problemId

    @Override
    public void save(Submission s) {

        String key = s.userId + "#" + s.problemId;

        if (!uniqueKey.add(key)) throw new IllegalStateException("User has already solved this problem");

        byUser.computeIfAbsent(s.userId, k -> Collections.synchronizedList(new ArrayList<>())).add(s);
        byProblem.computeIfAbsent(s.problemId, k -> Collections.synchronizedList(new ArrayList<>())).add(s);
    }

    @Override
    public List<Submission> findByUser(long userId) {
        return byUser.getOrDefault(userId, Collections.emptyList());
    }

    @Override
    public List<Submission> findByProblem(long problemId) {
        return byProblem.getOrDefault(problemId, Collections.emptyList());
    }

    @Override
    public Optional<Submission> findByUserAndProblem(long userId, long problemId) {
        return findByUser(userId).stream().filter(s -> s.problemId == problemId).findFirst();
    }

    @Override
    public Collection<Submission> findAll() {
        return byUser.values().stream().flatMap(List::stream).collect(Collectors.toList());
    }
}
