package org.example.repository;

import org.example.model.Submission;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

public interface SubmissionRepository {
    void save(Submission s);
    List<Submission> findByUser(long userId);
    List<Submission> findByProblem(long problemId);
    Optional<Submission> findByUserAndProblem(long userId, long problemId);
    Collection<Submission> findAll();
}

