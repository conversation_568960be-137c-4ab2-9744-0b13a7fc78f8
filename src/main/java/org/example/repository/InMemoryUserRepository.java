package org.example.repository;

import org.example.model.User;

import java.util.Collection;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

public class InMemoryUserRepository implements UserRepository {

    private Map<Long, User> store = new ConcurrentHashMap<>();
    private AtomicLong seq = new AtomicLong(1);

    @Override
    public User save(String name, String department) {
        long id = seq.getAndIncrement();
        User u = new User(id, name, department);
        store.put(id, u);
        return u;
    }

    @Override
    public Optional<User> findById(long id) {
        return Optional.ofNullable(store.get(id));
    }

    @Override
    public Collection<User> findAll() {
        return store.values();
    }
}


