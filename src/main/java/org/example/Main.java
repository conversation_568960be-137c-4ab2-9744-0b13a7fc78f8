package org.example;

import org.example.dto.ProblemQuery;
import org.example.dto.SortSpec;
import org.example.model.Difficulty;
import org.example.model.Problem;
import org.example.model.User;
import org.example.repository.*;
import org.example.scoring.FixedScoreStrategy;
import org.example.scoring.StrategyRegistry;
import org.example.scoring.TimeWeightedStrategy;
import org.example.service.ContestService;
import org.example.service.ProblemService;
import org.example.service.UserService;

import java.util.Map;
import java.util.Set;

//TIP To <b>Run</b> code, press <shortcut actionId="Run"/> or
// click the <icon src="AllIcons.Actions.Execute"/> icon in the gutter.
public class Main {
    public static void main(String[] args) {
        //TIP Press <shortcut actionId="ShowIntentionActions"/> with your caret at the highlighted text
        // to see how IntelliJ IDEA suggests fixing it.
        System.out.printf("Hello and welcome!");

        // Infra setup
        ProblemRepository problemRepo = new InMemoryProblemRepository();
        UserRepository userRepo = new InMemoryUserRepository();
        SubmissionRepository subRepo = new InMemorySubmissionRepository();
        StrategyRegistry strategies = new StrategyRegistry()
                .register(new FixedScoreStrategy())
                .register(new TimeWeightedStrategy(100, 1));

        ProblemService problemService = new ProblemService(problemRepo);
        UserService userService = new UserService(userRepo);

        long now = System.currentTimeMillis();
        long twoDaysMs = 2L * 24 * 60 * 60 * 1000;
        ContestService contest = new ContestService(
                problemService, userService, subRepo, strategies,
                now - 1_000, now + twoDaysMs // contest open right now for 2 days
        );

        // Seed data
        Problem p1 = problemService.addProblem("Two Sum", "Classic hashmap problem",
                Set.of("arrays", "hashmap"), Difficulty.EASY, 100, "fixed");
        Problem p2 = problemService.addProblem("LRU Cache", "Design an LRU cache",
                Set.of("design", "cache"), Difficulty.MEDIUM, 250, "time_weighted");
        Problem p3 = problemService.addProblem("Max Flow", "Edmonds–Karp implementation",
                Set.of("graphs"), Difficulty.HARD, 500, "time_weighted");

        User u1 = userService.addUser("Ram", "ML Platform");
        User u2 = userService.addUser("Kabir", "Payments");
        User u3 = userService.addUser("Meera", "Finance");

        // Fetch problems: filter & sort
        System.out.println("\nAll problems (sorted by score desc, name asc):");
        contest.fetchProblems(ProblemQuery.of()).forEach(System.out::println);

        System.out.println("\nMedium+ tag 'cache' sorted by avgTime asc:");
        contest.fetchProblems(ProblemQuery.of()
                        .difficulty(Difficulty.MEDIUM)
                        .tags(Set.of("cache"))
                        .sortBy(SortSpec.asc("avgTime")))
                .forEach(System.out::println);

        // Solve
        System.out.println("\nSolves: ");
        System.out.println(contest.solve(u1.id, p1.id, 120));   // 2 min
        System.out.println(contest.solve(u1.id, p2.id, 600));   // 10 min
        System.out.println(contest.solve(u2.id, p2.id, 300));   // 5 min
        System.out.println(contest.solve(u3.id, p3.id, 1_200)); // 20 min

        // Extension stats now visible
        System.out.println("\nProblems with extension stats (solvedCount, avgTime):");
        contest.fetchProblems(ProblemQuery.of().sortBy
                        (SortSpec.desc("solvedCount"), SortSpec.asc("name")))
                .forEach(System.out::println);


        // Solved problems for a user
        System.out.println("\nSolved by Aditi:");
        contest.fetchSolvedProblems(u1.id).forEach(s ->
                System.out.printf("Problem #%d, time=%ds, score=%d\n", s.problemId, s.timeTakenSec, s.awardedScore));

        // Leader
        Map.Entry<User, Integer> leader = contest.getLeader();
        System.out.printf("\nLeader: %s (%s) with %d points\n", leader.getKey().name, leader.getKey().department, leader.getValue());

        // Error handling demo (duplicate solve)
        try {
            contest.solve(u1.id, p1.id, 100);
        } catch (Exception e) {
            System.out.println("Expected error: " + e.getMessage());
        }

        // Invalid sort field demo
        try {
            contest.fetchProblems(ProblemQuery.of().sortBy(new SortSpec("random", true)));
        } catch (Exception e) {
            System.out.println("Expected error: " + e.getMessage());
        }
    }
}